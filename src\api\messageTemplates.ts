import { domain, token } from "@/constants";
import { DayTwoReminderType, InviteRegistrationType, SendInAppMessage, SendPollType, SendReminderType, SendSameDayReminderType, SessionReminderType, ThankYouMessageType, VisitBoothReminderType } from "@/types";
import axios from "axios";

// Helper function to convert data to FormData for file uploads
const createFormData = (data: any) => {
    const form = new FormData();

    Object.keys(data).forEach(key => {
        const value = data[key];
        if (value !== null && value !== undefined) {
            if (key === 'template_banner' && value instanceof File) {
                form.append(key, value);
            } else {
                form.append(key, String(value));
            }
        }
    });

    return form;
};

export const sendReminder = async (formData: SendReminderType) => {
    try {
        const form = createFormData(formData);
        const response = await axios.post(`${domain}/api/notifications`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send reminder");
        }
        throw new Error("An unexpected error occurred");
    }
}

export const sendSameDayReminder = async (formData: SendSameDayReminderType) => {
    try {
        const form = createFormData(formData);
        const response = await axios.post(`${domain}/api/notifications-samedayinvitation`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;
    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send same day reminder");
        }
        throw new Error("An unexpected error occurred");
    }
};

export const sessionReminder = async (formData: SessionReminderType) => {
    try {
        const form = createFormData(formData);
        const response = await axios.post(`${domain}/api/session-reminder`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;

    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send session reminder");
        }
        throw new Error("An unexpected error occurred");
    }
}

export const visitBoothReminder = async (formData: VisitBoothReminderType) => {
    try {
        const form = createFormData(formData);
        const response = await axios.post(`${domain}/api/reminder-to-visit-booth`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;

    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send visit booth reminder");
        }
        throw new Error("An unexpected error occurred");
    }
}

export const dayTwoReminder = async (formData: DayTwoReminderType) => {
    try {
        const form = createFormData(formData);
        const response = await axios.post(`${domain}/api/day-two-reminder`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;

    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send day two reminder");
        }
        throw new Error("An unexpected error occurred");
    }
}

export const dayTwoSameDayReminder = async (formData: DayTwoReminderType) => {
    try {
        const form = createFormData(formData);
        const response = await axios.post(`${domain}/api/day_two_same_day_reminder`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;

    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send day two same day reminder");
        }
        throw new Error("An unexpected error occurred");
    }
}


export const thankYouMessage = async (formData: ThankYouMessageType) => {
    try {
        const form = createFormData(formData);
        const response = await axios.post(`${domain}/api/thank-you-message`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;

    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send thank you message");
        }
        throw new Error("An unexpected error occurred");
    }
}

export const sendPoll = async (formData: SendPollType) => {
    try {
        const form = createFormData(formData);
        const response = await axios.post(`${domain}/api/notification-poll`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;

    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send thank you message");
        }
        throw new Error("An unexpected error occurred");
    }
}

export const sendInAppMessage = async (formData: SendInAppMessage) => {
    try {
        const response = await axios.post(`${domain}/api/custom-notification-message`, formData, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;

    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to send app message");
        }
        throw new Error("An unexpected error occurred");
    }
}

export const inviteRegistrations = async (formData: InviteRegistrationType) => {
    try {
        // Create FormData to handle file upload
        const form = new FormData();

        // Append all form fields
        Object.keys(formData).forEach(key => {
            const value = formData[key as keyof InviteRegistrationType];
            if (value !== null && value !== undefined) {
                if (key === 'template_banner' && value instanceof File) {
                    form.append(key, value);
                } else {
                    form.append(key, String(value));
                }
            }
        });

        const response = await axios.post(`${domain}/api/invitation-request-message`, form, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'Authorization': `Bearer ${token}`,
            }
        });

        return response.data;

    } catch (error) {
        if (axios.isAxiosError(error)) {
            throw new Error(error.response?.data?.message || "Failed to invite registrations");
        }
        throw new Error("An unexpected error occurred");
    }
}